# QMT A股财务数据获取 - 简化版
# 直接在QMT策略中运行

import pandas as pd
import time
from datetime import datetime

def get_a_stock_financial_data_simple(ContextInfo):
    """
    简化版A股财务数据获取函数
    适合在QMT策略中直接调用
    """
    print("开始获取A股财务数据...")
    
    # 1. 获取股票列表
    stock_list = []
    try:
        # 获取主要指数成分股
        hs300 = ContextInfo.get_sector('000300.SH')  # 沪深300
        zz500 = ContextInfo.get_sector('000905.SH')  # 中证500
        sz50 = ContextInfo.get_sector('000016.SH')   # 上证50
        
        # 合并并去重
        stock_list = list(set(hs300 + zz500 + sz50))
        print(f"获取到 {len(stock_list)} 只股票")
        
    except Exception as e:
        print(f"获取股票列表失败: {e}")
        # 使用备用列表
        stock_list = [
            '000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ', '300014.SZ',
            '600000.SH', '600036.SH', '600519.SH', '601166.SH', '601318.SH'
        ]
        print(f"使用备用股票列表，共 {len(stock_list)} 只")
    
    # 2. 定义财务字段
    financial_fields = [
        # 资产负债表
        'ASHAREBALANCESHEET.tot_assets',           # 总资产
        'ASHAREBALANCESHEET.tot_shrhldr_eqy_excl_min_int', # 股东权益
        'ASHAREBALANCESHEET.monetary_cap',         # 货币资金
        
        # 利润表
        'ASHAREINCOME.oper_rev',                   # 营业收入
        'ASHAREINCOME.net_profit_incl_min_int_inc', # 归母净利润
        'ASHAREINCOME.basic_eps',                  # 基本每股收益
        
        # 现金流量表
        'ASHARECASHFLOW.net_cash_flows_oper_act',  # 经营现金流
        
        # 股本结构
        'CAPITALSTRUCTURE.total_capital',          # 总股本
    ]
    
    # 3. 设置时间范围（最近2年）
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = datetime.now().replace(year=datetime.now().year-2).strftime('%Y%m%d')
    
    print(f"数据时间范围: {start_date} 到 {end_date}")
    
    # 4. 分批获取数据
    batch_size = 50
    all_results = []
    
    for i in range(0, len(stock_list), batch_size):
        batch_stocks = stock_list[i:i+batch_size]
        batch_num = i//batch_size + 1
        
        print(f"正在处理第 {batch_num} 批股票 ({len(batch_stocks)} 只)...")
        
        try:
            # 获取财务数据
            data = ContextInfo.get_financial_data(
                fieldList=financial_fields,
                stockList=batch_stocks,
                startDate=start_date,
                endDate=end_date,
                report_type='announce_time'
            )
            
            if data is not None:
                all_results.append(data)
                print(f"第 {batch_num} 批数据获取成功")
            else:
                print(f"第 {batch_num} 批数据为空")
                
        except Exception as e:
            print(f"第 {batch_num} 批数据获取失败: {e}")
            continue
        
        # 延时1秒
        time.sleep(1)
    
    # 5. 保存数据
    if all_results:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        try:
            # 保存为pickle文件（推荐，保持原始数据结构）
            import pickle
            pickle_file = f'D:/QMT/UserData/A股财务数据_{timestamp}.pkl'
            with open(pickle_file, 'wb') as f:
                pickle.dump(all_results, f)
            print(f"数据已保存到: {pickle_file}")
            
            # 如果数据是DataFrame格式，也保存为CSV
            if all_results and isinstance(all_results[0], pd.DataFrame):
                combined_df = pd.concat(all_results, ignore_index=True)
                csv_file = f'D:/QMT/UserData/A股财务数据_{timestamp}.csv'
                combined_df.to_csv(csv_file, encoding='utf-8-sig', index=False)
                print(f"CSV文件已保存到: {csv_file}")
                
        except Exception as e:
            print(f"保存数据失败: {e}")
    
    print("财务数据获取完成！")
    return all_results

def get_specific_financial_data(ContextInfo, stock_codes, fields=None):
    """
    获取指定股票的财务数据
    
    参数:
    stock_codes: 股票代码列表，如 ['000001.SZ', '600000.SH']
    fields: 财务字段列表，如果为None则使用默认字段
    """
    if fields is None:
        fields = [
            'ASHAREBALANCESHEET.tot_assets',           # 总资产
            'ASHAREINCOME.oper_rev',                   # 营业收入
            'ASHAREINCOME.net_profit_incl_min_int_inc', # 归母净利润
            'ASHAREINCOME.basic_eps',                  # 基本每股收益
        ]
    
    # 时间范围：最近1年
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = datetime.now().replace(year=datetime.now().year-1).strftime('%Y%m%d')
    
    try:
        data = ContextInfo.get_financial_data(
            fieldList=fields,
            stockList=stock_codes,
            startDate=start_date,
            endDate=end_date,
            report_type='announce_time'
        )
        
        print(f"获取 {len(stock_codes)} 只股票的财务数据成功")
        
        # 保存数据
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'D:/QMT/UserData/指定股票财务数据_{timestamp}.pkl'
        
        import pickle
        with open(filename, 'wb') as f:
            pickle.dump(data, f)
        print(f"数据已保存到: {filename}")
        
        return data
        
    except Exception as e:
        print(f"获取财务数据失败: {e}")
        return None

def analyze_financial_data(data):
    """
    简单的财务数据分析
    """
    if not data:
        print("没有数据可分析")
        return
    
    print("\n=== 财务数据分析 ===")
    
    try:
        if isinstance(data, list) and len(data) > 0:
            first_data = data[0]
            if isinstance(first_data, pd.DataFrame):
                print(f"数据行数: {len(first_data)}")
                print(f"数据列数: {len(first_data.columns)}")
                print("\n数据列名:")
                for col in first_data.columns:
                    print(f"  - {col}")
                    
                # 显示前几行数据
                print("\n前5行数据:")
                print(first_data.head())
                
            elif isinstance(first_data, pd.Panel):
                print("数据格式: Panel")
                print(f"股票数量: {len(first_data.items)}")
                print(f"时间点数量: {len(first_data.major_axis)}")
                print(f"字段数量: {len(first_data.minor_axis)}")
                
        print("\n分析完成")
        
    except Exception as e:
        print(f"数据分析失败: {e}")

# QMT策略函数
def init(ContextInfo):
    """策略初始化"""
    print("=== A股财务数据获取策略 ===")
    print("功能：获取A股主要股票的财务数据")

def handlebar(ContextInfo):
    """策略主函数"""
    print("\n开始执行财务数据获取...")
    
    # 方式1：获取所有主要股票的财务数据
    all_data = get_a_stock_financial_data_simple(ContextInfo)
    
    # 方式2：获取指定股票的财务数据（可选）
    # specific_stocks = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']
    # specific_data = get_specific_financial_data(ContextInfo, specific_stocks)
    
    # 简单分析数据
    if all_data:
        analyze_financial_data(all_data)
    
    return all_data

# 使用示例
def example_usage():
    """使用示例（仅供参考，需要在QMT环境中运行）"""
    print("=== 使用示例 ===")
    print("1. 在QMT中创建新策略")
    print("2. 复制此代码到策略编辑器")
    print("3. 运行策略")
    print("4. 查看控制台输出和保存的文件")
    print("\n数据文件保存位置: D:/QMT/UserData/")
    print("文件格式: .pkl (推荐) 和 .csv")

if __name__ == "__main__":
    example_usage()
