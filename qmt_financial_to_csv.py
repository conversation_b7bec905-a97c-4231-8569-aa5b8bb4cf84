# QMT A股财务数据获取并转换为CSV格式
# 专门优化CSV输出的版本

import pandas as pd
import time
from datetime import datetime
import os

def get_a_stock_financial_to_csv(ContextInfo):
    """
    获取A股财务数据并直接保存为CSV格式
    """
    print("开始获取A股财务数据并转换为CSV...")
    
    # 1. 获取股票列表
    stock_list = get_stock_list(ContextInfo)
    if not stock_list:
        print("无法获取股票列表，程序退出")
        return None
    
    # 2. 定义财务字段
    financial_fields = get_financial_fields()
    
    # 3. 设置时间范围
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = datetime.now().replace(year=datetime.now().year-2).strftime('%Y%m%d')
    
    print(f"数据时间范围: {start_date} 到 {end_date}")
    print(f"财务字段数量: {len(financial_fields)}")
    
    # 4. 创建保存目录
    save_dir = 'D:/QMT/UserData/CSV_Data/'
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
        print(f"创建目录: {save_dir}")
    
    # 5. 分批获取并转换数据
    batch_size = 30  # 减小批次大小，便于CSV转换
    all_csv_data = []
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    for i in range(0, len(stock_list), batch_size):
        batch_stocks = stock_list[i:i+batch_size]
        batch_num = i//batch_size + 1
        
        print(f"\n处理第 {batch_num} 批股票 ({len(batch_stocks)} 只)...")
        
        try:
            # 获取财务数据
            financial_data = ContextInfo.get_financial_data(
                fieldList=financial_fields,
                stockList=batch_stocks,
                startDate=start_date,
                endDate=end_date,
                report_type='announce_time'
            )
            
            if financial_data is not None:
                # 转换为CSV友好的格式
                csv_data = convert_to_csv_format(financial_data, batch_stocks, financial_fields)
                
                if csv_data is not None and not csv_data.empty:
                    all_csv_data.append(csv_data)
                    print(f"✓ 第 {batch_num} 批数据转换成功，{len(csv_data)} 行数据")
                    
                    # 保存单批数据（备份）
                    batch_filename = f"{save_dir}批次{batch_num}_财务数据_{timestamp}.csv"
                    csv_data.to_csv(batch_filename, encoding='utf-8-sig', index=False)
                    print(f"  批次数据已保存: {batch_filename}")
                else:
                    print(f"✗ 第 {batch_num} 批数据转换失败")
            else:
                print(f"✗ 第 {batch_num} 批数据获取失败")
                
        except Exception as e:
            print(f"✗ 第 {batch_num} 批处理出错: {e}")
            continue
        
        # 延时
        time.sleep(1)
    
    # 6. 合并所有数据并保存最终CSV
    if all_csv_data:
        try:
            print(f"\n合并 {len(all_csv_data)} 批数据...")
            final_df = pd.concat(all_csv_data, ignore_index=True)
            
            # 保存最终CSV文件
            final_filename = f"{save_dir}A股财务数据汇总_{timestamp}.csv"
            final_df.to_csv(final_filename, encoding='utf-8-sig', index=False)
            
            print(f"\n=== 数据获取完成 ===")
            print(f"总数据行数: {len(final_df)}")
            print(f"总数据列数: {len(final_df.columns)}")
            print(f"最终CSV文件: {final_filename}")
            
            # 显示数据预览
            print(f"\n数据预览:")
            print(final_df.head())
            
            # 保存数据字典
            save_data_dictionary(final_df.columns, save_dir, timestamp)
            
            return final_df
            
        except Exception as e:
            print(f"合并数据失败: {e}")
            return all_csv_data
    else:
        print("没有获取到任何数据")
        return None

def get_stock_list(ContextInfo):
    """获取股票列表"""
    stock_list = []
    
    try:
        # 获取主要指数成分股
        indices_info = [
            ('000300.SH', '沪深300'),
            ('000905.SH', '中证500'),
            ('000016.SH', '上证50'),
            ('399006.SZ', '创业板指'),
        ]
        
        for index_code, index_name in indices_info:
            try:
                stocks = ContextInfo.get_sector(index_code)
                if stocks:
                    stock_list.extend(stocks)
                    print(f"从{index_name}获取 {len(stocks)} 只股票")
            except Exception as e:
                print(f"获取{index_name}失败: {e}")
                continue
        
        # 去重
        stock_list = list(set(stock_list))
        print(f"总共获取 {len(stock_list)} 只不重复股票")
        
    except Exception as e:
        print(f"获取股票列表失败: {e}")
        # 备用股票列表
        stock_list = [
            '000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ', '300014.SZ',
            '600000.SH', '600036.SH', '600519.SH', '601166.SH', '601318.SH',
            '000568.SZ', '002304.SZ', '300059.SZ', '600036.SH', '000895.SZ'
        ]
        print(f"使用备用列表: {len(stock_list)} 只股票")
    
    return stock_list

def get_financial_fields():
    """定义财务字段"""
    return [
        # 资产负债表
        'ASHAREBALANCESHEET.tot_assets',           # 总资产
        'ASHAREBALANCESHEET.tot_liab',             # 总负债
        'ASHAREBALANCESHEET.tot_shrhldr_eqy_excl_min_int', # 股东权益
        'ASHAREBALANCESHEET.monetary_cap',         # 货币资金
        'ASHAREBALANCESHEET.inventories',          # 存货
        'ASHAREBALANCESHEET.fix_assets',           # 固定资产
        
        # 利润表
        'ASHAREINCOME.oper_rev',                   # 营业收入
        'ASHAREINCOME.oper_cost',                  # 营业成本
        'ASHAREINCOME.net_profit_incl_min_int_inc', # 归母净利润
        'ASHAREINCOME.tot_profit',                 # 利润总额
        'ASHAREINCOME.basic_eps',                  # 基本每股收益
        
        # 现金流量表
        'ASHARECASHFLOW.net_cash_flows_oper_act',  # 经营现金流
        
        # 股本结构
        'CAPITALSTRUCTURE.total_capital',          # 总股本
    ]

def convert_to_csv_format(financial_data, stock_codes, field_names):
    """
    将QMT财务数据转换为CSV友好的DataFrame格式
    """
    try:
        # 根据不同的数据类型进行转换
        if isinstance(financial_data, pd.DataFrame):
            # 如果已经是DataFrame，直接返回
            print("  数据已是DataFrame格式")
            return financial_data
            
        elif isinstance(financial_data, pd.Panel):
            # Panel格式转换为DataFrame
            print("  转换Panel格式数据...")
            rows = []
            
            for stock in financial_data.items:
                stock_data = financial_data[stock]
                for date in stock_data.index:
                    row = {'股票代码': stock, '日期': date}
                    for field in stock_data.columns:
                        row[field] = stock_data.loc[date, field]
                    rows.append(row)
            
            df = pd.DataFrame(rows)
            print(f"  Panel转换完成: {len(df)} 行")
            return df
            
        elif isinstance(financial_data, dict):
            # 字典格式转换
            print("  转换字典格式数据...")
            rows = []
            
            for stock_code in stock_codes:
                if stock_code in financial_data:
                    stock_data = financial_data[stock_code]
                    if isinstance(stock_data, dict):
                        row = {'股票代码': stock_code}
                        row.update(stock_data)
                        rows.append(row)
                    elif isinstance(stock_data, pd.Series):
                        row = {'股票代码': stock_code}
                        row.update(stock_data.to_dict())
                        rows.append(row)
            
            df = pd.DataFrame(rows)
            print(f"  字典转换完成: {len(df)} 行")
            return df
            
        elif isinstance(financial_data, pd.Series):
            # Series格式转换
            print("  转换Series格式数据...")
            df = pd.DataFrame([financial_data.to_dict()])
            if len(stock_codes) == 1:
                df['股票代码'] = stock_codes[0]
            return df
            
        else:
            print(f"  未知数据格式: {type(financial_data)}")
            # 尝试直接转换为DataFrame
            df = pd.DataFrame(financial_data)
            return df
            
    except Exception as e:
        print(f"  数据转换失败: {e}")
        return None

def save_data_dictionary(columns, save_dir, timestamp):
    """保存数据字典说明"""
    try:
        field_descriptions = {
            'ASHAREBALANCESHEET.tot_assets': '总资产',
            'ASHAREBALANCESHEET.tot_liab': '总负债',
            'ASHAREBALANCESHEET.tot_shrhldr_eqy_excl_min_int': '股东权益合计',
            'ASHAREBALANCESHEET.monetary_cap': '货币资金',
            'ASHAREBALANCESHEET.inventories': '存货',
            'ASHAREBALANCESHEET.fix_assets': '固定资产',
            'ASHAREINCOME.oper_rev': '营业收入',
            'ASHAREINCOME.oper_cost': '营业成本',
            'ASHAREINCOME.net_profit_incl_min_int_inc': '归母净利润',
            'ASHAREINCOME.tot_profit': '利润总额',
            'ASHAREINCOME.basic_eps': '基本每股收益',
            'ASHARECASHFLOW.net_cash_flows_oper_act': '经营活动现金流量净额',
            'CAPITALSTRUCTURE.total_capital': '总股本',
        }
        
        dict_data = []
        for col in columns:
            description = field_descriptions.get(col, '未知字段')
            dict_data.append({'字段名': col, '中文说明': description})
        
        dict_df = pd.DataFrame(dict_data)
        dict_filename = f"{save_dir}数据字典_{timestamp}.csv"
        dict_df.to_csv(dict_filename, encoding='utf-8-sig', index=False)
        print(f"数据字典已保存: {dict_filename}")
        
    except Exception as e:
        print(f"保存数据字典失败: {e}")

def analyze_csv_data(df):
    """分析CSV数据"""
    if df is None or df.empty:
        print("没有数据可分析")
        return
    
    print(f"\n=== CSV数据分析 ===")
    print(f"数据行数: {len(df)}")
    print(f"数据列数: {len(df.columns)}")
    
    # 检查股票数量
    if '股票代码' in df.columns:
        unique_stocks = df['股票代码'].nunique()
        print(f"包含股票数量: {unique_stocks}")
    
    # 检查数据完整性
    missing_data = df.isnull().sum()
    if missing_data.sum() > 0:
        print(f"\n缺失数据统计:")
        for col, missing_count in missing_data.items():
            if missing_count > 0:
                print(f"  {col}: {missing_count} 个缺失值")
    else:
        print("数据完整，无缺失值")

# QMT策略函数
def init(ContextInfo):
    """策略初始化"""
    print("=== A股财务数据CSV导出策略 ===")
    print("功能：获取A股财务数据并保存为CSV格式")

def handlebar(ContextInfo):
    """策略主函数"""
    print("\n开始执行财务数据CSV导出...")
    
    # 获取数据并转换为CSV
    csv_data = get_a_stock_financial_to_csv(ContextInfo)
    
    # 分析数据
    if csv_data is not None:
        analyze_csv_data(csv_data)
        print("\n=== 任务完成 ===")
        print("请查看 D:/QMT/UserData/CSV_Data/ 目录下的CSV文件")
    else:
        print("\n=== 任务失败 ===")
    
    return csv_data

# 使用说明
if __name__ == "__main__":
    print("=== QMT财务数据CSV导出工具 ===")
    print("使用方法：")
    print("1. 在QMT中创建新策略")
    print("2. 复制此代码到策略编辑器")
    print("3. 运行策略")
    print("4. 查看 D:/QMT/UserData/CSV_Data/ 目录下的CSV文件")
    print("\n输出文件：")
    print("- A股财务数据汇总_时间戳.csv (主要文件)")
    print("- 批次N_财务数据_时间戳.csv (备份文件)")
    print("- 数据字典_时间戳.csv (字段说明)")
