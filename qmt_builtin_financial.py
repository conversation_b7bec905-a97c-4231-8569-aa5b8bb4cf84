# 在QMT内置Python环境中运行
import pandas as pd
import time
from datetime import datetime

def get_all_a_stock_financial_data():
    """
    获取A股所有股票的财务数据
    """
    print("开始获取A股所有股票财务数据...")

    # 获取A股所有股票代码
    # 使用QMT内置函数获取沪深两市股票列表
    sh_stocks = []  # 上海A股
    sz_stocks = []  # 深圳A股

    try:
        # 获取沪深300成分股作为示例（你可以根据需要调整）
        # 实际使用时可以通过其他方式获取完整的A股列表
        hs300_stocks = ContextInfo.get_sector('000300.SH')
        zz500_stocks = ContextInfo.get_sector('000905.SH')
        sz50_stocks = ContextInfo.get_sector('000016.SH')

        # 合并股票列表并去重
        all_stocks = list(set(hs300_stocks + zz500_stocks + sz50_stocks))

        print(f"获取到 {len(all_stocks)} 只股票")

    except Exception as e:
        print(f"获取股票列表失败: {e}")
        # 如果获取失败，使用一些示例股票代码
        all_stocks = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']
        print(f"使用示例股票列表，共 {len(all_stocks)} 只股票")

    # 定义需要获取的财务数据字段
    # 资产负债表字段
    balance_fields = [
        'ASHAREBALANCESHEET.tot_assets',           # 总资产
        'ASHAREBALANCESHEET.tot_liab',             # 总负债
        'ASHAREBALANCESHEET.tot_shrhldr_eqy_excl_min_int', # 股东权益合计
        'ASHAREBALANCESHEET.monetary_cap',         # 货币资金
        'ASHAREBALANCESHEET.inventories',          # 存货
        'ASHAREBALANCESHEET.fix_assets',           # 固定资产
        'ASHAREBALANCESHEET.goodwill',             # 商誉
    ]

    # 利润表字段
    income_fields = [
        'ASHAREINCOME.oper_rev',                   # 营业收入
        'ASHAREINCOME.oper_cost',                  # 营业成本
        'ASHAREINCOME.net_profit_excl_min_int_inc', # 净利润
        'ASHAREINCOME.net_profit_incl_min_int_inc', # 归母净利润
        'ASHAREINCOME.tot_profit',                 # 利润总额
        'ASHAREINCOME.oper_profit',                # 营业利润
        'ASHAREINCOME.basic_eps',                  # 基本每股收益
    ]

    # 现金流量表字段
    cashflow_fields = [
        'ASHARECASHFLOW.net_cash_flows_oper_act',  # 经营活动现金流量净额
        'ASHARECASHFLOW.net_cash_flows_inv_act',   # 投资活动现金流量净额
        'ASHARECASHFLOW.net_cash_flows_fnc_act',   # 筹资活动现金流量净额
    ]

    # 合并所有字段
    all_fields = balance_fields + income_fields + cashflow_fields

    # 设置时间范围（获取最近2年的数据）
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now().replace(year=datetime.now().year-2)).strftime('%Y%m%d')

    print(f"获取时间范围: {start_date} 到 {end_date}")

    # 分批获取财务数据（避免一次性获取太多数据）
    batch_size = 50  # 每批处理50只股票
    all_financial_data = []

    for i in range(0, len(all_stocks), batch_size):
        batch_stocks = all_stocks[i:i+batch_size]
        print(f"正在处理第 {i//batch_size + 1} 批股票 ({len(batch_stocks)} 只)...")

        try:
            # 获取财务数据
            financial_data = ContextInfo.get_financial_data(
                fieldList=all_fields,
                stockList=batch_stocks,
                startDate=start_date,
                endDate=end_date,
                report_type='announce_time'  # 按公告期获取数据
            )

            if financial_data is not None:
                all_financial_data.append(financial_data)
                print(f"第 {i//batch_size + 1} 批数据获取成功")
            else:
                print(f"第 {i//batch_size + 1} 批数据获取失败")

        except Exception as e:
            print(f"第 {i//batch_size + 1} 批数据获取出错: {e}")
            continue

        # 添加延时避免请求过快
        time.sleep(1)

    # 合并所有数据
    if all_financial_data:
        try:
            # 根据数据类型进行合并
            if isinstance(all_financial_data[0], pd.DataFrame):
                combined_data = pd.concat(all_financial_data, ignore_index=True)
            elif isinstance(all_financial_data[0], pd.Panel):
                # 如果是Panel类型，需要特殊处理
                combined_data = all_financial_data[0]
                for data in all_financial_data[1:]:
                    # Panel合并逻辑
                    pass
            else:
                combined_data = all_financial_data

            print("数据合并完成")

        except Exception as e:
            print(f"数据合并失败: {e}")
            combined_data = all_financial_data
    else:
        print("没有获取到任何财务数据")
        return None

    # 保存数据到文件
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        if isinstance(combined_data, pd.DataFrame):
            # 保存为CSV文件
            filename = f'D:/QMT/UserData/A股财务数据_{timestamp}.csv'
            combined_data.to_csv(filename, encoding='utf-8-sig', index=False)
            print(f"财务数据已保存到: {filename}")

            # 保存为Excel文件（如果数据不太大）
            if len(combined_data) < 100000:  # 限制Excel文件大小
                excel_filename = f'D:/QMT/UserData/A股财务数据_{timestamp}.xlsx'
                combined_data.to_excel(excel_filename, index=False)
                print(f"财务数据已保存到: {excel_filename}")

        elif isinstance(combined_data, list):
            # 如果是列表，保存为pickle文件
            import pickle
            pickle_filename = f'D:/QMT/UserData/A股财务数据_{timestamp}.pkl'
            with open(pickle_filename, 'wb') as f:
                pickle.dump(combined_data, f)
            print(f"财务数据已保存到: {pickle_filename}")

    except Exception as e:
        print(f"保存数据失败: {e}")

    print("A股财务数据获取完成！")
    return combined_data

def get_specific_stocks_financial_data(stock_list, fields=None):
    """
    获取指定股票的财务数据

    参数:
    stock_list: 股票代码列表，如 ['000001.SZ', '600000.SH']
    fields: 财务字段列表，如果为None则使用默认字段
    """
    if fields is None:
        # 默认字段
        fields = [
            'ASHAREBALANCESHEET.tot_assets',           # 总资产
            'ASHAREBALANCESHEET.tot_shrhldr_eqy_excl_min_int', # 股东权益
            'ASHAREINCOME.oper_rev',                   # 营业收入
            'ASHAREINCOME.net_profit_incl_min_int_inc', # 归母净利润
            'ASHAREINCOME.basic_eps',                  # 基本每股收益
        ]

    # 设置时间范围
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now().replace(year=datetime.now().year-1)).strftime('%Y%m%d')

    try:
        financial_data = ContextInfo.get_financial_data(
            fieldList=fields,
            stockList=stock_list,
            startDate=start_date,
            endDate=end_date,
            report_type='announce_time'
        )

        print(f"获取 {len(stock_list)} 只股票的财务数据成功")
        return financial_data

    except Exception as e:
        print(f"获取财务数据失败: {e}")
        return None

# 在QMT策略中调用的主函数
def init(ContextInfo):
    """QMT策略初始化函数"""
    print("财务数据获取策略初始化...")

def handlebar(ContextInfo):
    """QMT策略主函数"""
    # 获取所有A股财务数据
    all_data = get_all_a_stock_financial_data()

    # 或者获取指定股票的财务数据
    # specific_stocks = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']
    # specific_data = get_specific_stocks_financial_data(specific_stocks)

    return all_data

# 如果直接运行此脚本（在QMT环境外测试时）
if __name__ == "__main__":
    print("请在QMT环境中运行此脚本")
    print("使用方法：")
    print("1. 将此脚本复制到QMT策略中")
    print("2. 运行策略即可获取A股财务数据")
    print("3. 数据将保存到 D:/QMT/UserData/ 目录下")