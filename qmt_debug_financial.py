# QMT财务数据获取调试版本
# 用于排查数据获取问题

import pandas as pd
import time
from datetime import datetime
import os

def debug_financial_data(ContextInfo):
    """
    调试版财务数据获取函数
    详细输出每一步的执行情况
    """
    print("=== QMT财务数据获取调试模式 ===")
    
    # 1. 检查ContextInfo对象
    print("1. 检查ContextInfo对象...")
    try:
        print(f"   ContextInfo类型: {type(ContextInfo)}")
        print(f"   ContextInfo可用方法: {[method for method in dir(ContextInfo) if not method.startswith('_')]}")
    except Exception as e:
        print(f"   ContextInfo检查失败: {e}")
        return None
    
    # 2. 测试获取单个指数成分股
    print("\n2. 测试获取指数成分股...")
    test_indices = ['000300.SH', '000905.SH', '000016.SH']
    available_stocks = []
    
    for index_code in test_indices:
        try:
            stocks = ContextInfo.get_sector(index_code)
            if stocks:
                print(f"   ✓ {index_code}: 获取到 {len(stocks)} 只股票")
                print(f"     前5只: {stocks[:5]}")
                available_stocks.extend(stocks[:10])  # 只取前10只用于测试
            else:
                print(f"   ✗ {index_code}: 未获取到股票")
        except Exception as e:
            print(f"   ✗ {index_code}: 获取失败 - {e}")
    
    if not available_stocks:
        print("   使用备用测试股票...")
        available_stocks = ['000001.SZ', '000002.SZ', '600000.SH']
    
    print(f"   最终测试股票列表: {available_stocks}")
    
    # 3. 测试单只股票财务数据获取
    print("\n3. 测试单只股票财务数据获取...")
    test_stock = available_stocks[0]
    test_fields = [
        'ASHAREINCOME.oper_rev',                   # 营业收入
        'ASHAREINCOME.net_profit_incl_min_int_inc', # 归母净利润
    ]
    
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = datetime.now().replace(year=datetime.now().year-1).strftime('%Y%m%d')
    
    print(f"   测试股票: {test_stock}")
    print(f"   测试字段: {test_fields}")
    print(f"   时间范围: {start_date} 到 {end_date}")
    
    try:
        single_data = ContextInfo.get_financial_data(
            fieldList=test_fields,
            stockList=[test_stock],
            startDate=start_date,
            endDate=end_date,
            report_type='announce_time'
        )
        
        print(f"   ✓ 单只股票数据获取成功")
        print(f"   数据类型: {type(single_data)}")
        
        if single_data is not None:
            if isinstance(single_data, pd.DataFrame):
                print(f"   DataFrame形状: {single_data.shape}")
                print(f"   DataFrame列名: {list(single_data.columns)}")
                print(f"   前几行数据:")
                print(single_data.head())
            elif isinstance(single_data, pd.Panel):
                print(f"   Panel维度: items={len(single_data.items)}, major_axis={len(single_data.major_axis)}, minor_axis={len(single_data.minor_axis)}")
                print(f"   Panel items: {list(single_data.items)}")
                print(f"   Panel major_axis: {list(single_data.major_axis)}")
                print(f"   Panel minor_axis: {list(single_data.minor_axis)}")
            elif isinstance(single_data, dict):
                print(f"   字典键: {list(single_data.keys())}")
                for key, value in single_data.items():
                    print(f"   {key}: {type(value)} - {value}")
            else:
                print(f"   数据内容: {single_data}")
        else:
            print("   ✗ 数据为None")
            
    except Exception as e:
        print(f"   ✗ 单只股票数据获取失败: {e}")
        return None
    
    # 4. 测试多只股票财务数据获取
    print("\n4. 测试多只股票财务数据获取...")
    test_stocks = available_stocks[:3]  # 只测试前3只
    
    try:
        multi_data = ContextInfo.get_financial_data(
            fieldList=test_fields,
            stockList=test_stocks,
            startDate=start_date,
            endDate=end_date,
            report_type='announce_time'
        )
        
        print(f"   ✓ 多只股票数据获取成功")
        print(f"   数据类型: {type(multi_data)}")
        
        if multi_data is not None:
            analyze_data_structure(multi_data, "多只股票数据")
        else:
            print("   ✗ 多只股票数据为None")
            
    except Exception as e:
        print(f"   ✗ 多只股票数据获取失败: {e}")
    
    # 5. 测试数据转换为CSV格式
    print("\n5. 测试数据转换...")
    if single_data is not None:
        csv_data = convert_data_to_csv_debug(single_data, [test_stock], test_fields)
        if csv_data is not None:
            print("   ✓ CSV转换成功")
            save_debug_csv(csv_data, "debug_single_stock")
        else:
            print("   ✗ CSV转换失败")
    
    # 6. 检查保存目录
    print("\n6. 检查保存目录...")
    check_directories()
    
    print("\n=== 调试完成 ===")
    return single_data

def analyze_data_structure(data, data_name):
    """分析数据结构"""
    print(f"   分析 {data_name}:")
    
    if isinstance(data, pd.DataFrame):
        print(f"     DataFrame: {data.shape}")
        print(f"     列名: {list(data.columns)}")
        if not data.empty:
            print(f"     前3行:")
            print(data.head(3).to_string())
    
    elif isinstance(data, pd.Panel):
        print(f"     Panel维度: {data.shape}")
        print(f"     Items: {list(data.items)}")
        print(f"     Major_axis: {list(data.major_axis)[:5]}...")
        print(f"     Minor_axis: {list(data.minor_axis)}")
        
        # 显示第一个股票的数据
        if len(data.items) > 0:
            first_stock = data.items[0]
            stock_data = data[first_stock]
            print(f"     {first_stock}的数据:")
            print(stock_data.head(3).to_string())
    
    elif isinstance(data, dict):
        print(f"     字典键数量: {len(data)}")
        print(f"     键: {list(data.keys())[:5]}...")
        for key, value in list(data.items())[:2]:
            print(f"     {key}: {type(value)}")
            if isinstance(value, (pd.DataFrame, pd.Series)):
                print(f"       形状: {value.shape}")
    
    elif isinstance(data, pd.Series):
        print(f"     Series长度: {len(data)}")
        print(f"     索引: {list(data.index)[:5]}...")
        print(f"     前3个值: {data.head(3).to_dict()}")
    
    else:
        print(f"     未知类型: {type(data)}")
        print(f"     内容: {str(data)[:200]}...")

def convert_data_to_csv_debug(data, stock_codes, field_names):
    """调试版数据转换函数"""
    print("   开始CSV转换...")
    
    try:
        rows = []
        
        if isinstance(data, pd.DataFrame):
            print("     处理DataFrame格式...")
            for index, row in data.iterrows():
                row_dict = {'股票代码': stock_codes[0] if stock_codes else '未知', '索引': str(index)}
                row_dict.update(row.to_dict())
                rows.append(row_dict)
                
        elif isinstance(data, pd.Panel):
            print("     处理Panel格式...")
            for stock in data.items:
                stock_data = data[stock]
                for date_idx in stock_data.index:
                    row_dict = {'股票代码': stock, '日期': str(date_idx)}
                    for field in stock_data.columns:
                        clean_field = field.split('.')[-1] if '.' in field else field
                        row_dict[clean_field] = stock_data.loc[date_idx, field]
                    rows.append(row_dict)
                    
        elif isinstance(data, dict):
            print("     处理字典格式...")
            for stock_code in stock_codes:
                if stock_code in data:
                    stock_data = data[stock_code]
                    row_dict = {'股票代码': stock_code}
                    if isinstance(stock_data, dict):
                        row_dict.update(stock_data)
                    elif isinstance(stock_data, pd.Series):
                        row_dict.update(stock_data.to_dict())
                    rows.append(row_dict)
                    
        elif isinstance(data, pd.Series):
            print("     处理Series格式...")
            row_dict = {'股票代码': stock_codes[0] if stock_codes else '未知'}
            row_dict.update(data.to_dict())
            rows.append(row_dict)
        
        if rows:
            df = pd.DataFrame(rows)
            print(f"     转换成功: {len(df)} 行, {len(df.columns)} 列")
            print(f"     列名: {list(df.columns)}")
            return df
        else:
            print("     转换失败: 没有生成行数据")
            return None
            
    except Exception as e:
        print(f"     转换异常: {e}")
        return None

def save_debug_csv(df, filename_prefix):
    """保存调试CSV文件"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'D:/QMT/UserData/{filename_prefix}_{timestamp}.csv'
        
        df.to_csv(filename, encoding='utf-8-sig', index=False)
        print(f"   调试CSV已保存: {filename}")
        
        # 显示文件内容预览
        print(f"   文件内容预览:")
        print(df.head().to_string())
        
    except Exception as e:
        print(f"   保存CSV失败: {e}")

def check_directories():
    """检查目录状态"""
    directories = [
        'D:/QMT/UserData/',
        'D:/QMT/UserData/CSV_Data/',
        'D:/QMT/UserData/FinancialData/'
    ]
    
    for directory in directories:
        try:
            if os.path.exists(directory):
                files = os.listdir(directory)
                print(f"   ✓ {directory} 存在，包含 {len(files)} 个文件")
                if files:
                    print(f"     最新文件: {sorted(files)[-1]}")
            else:
                print(f"   ✗ {directory} 不存在")
                try:
                    os.makedirs(directory)
                    print(f"     已创建目录: {directory}")
                except Exception as e:
                    print(f"     创建目录失败: {e}")
        except Exception as e:
            print(f"   检查目录失败: {e}")

def simple_test_financial_data(ContextInfo):
    """
    最简单的测试函数
    只获取一只股票的一个字段
    """
    print("=== 最简单测试 ===")
    
    try:
        # 最简单的调用
        data = ContextInfo.get_financial_data(
            fieldList=['ASHAREINCOME.oper_rev'],  # 只要营业收入
            stockList=['000001.SZ'],              # 只要平安银行
            startDate='20230101',                 # 固定日期
            endDate='20231231',
            report_type='announce_time'
        )
        
        print(f"数据类型: {type(data)}")
        print(f"数据内容: {data}")
        
        if data is not None:
            # 直接保存原始数据
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 保存为pickle
            import pickle
            pickle_file = f'D:/QMT/UserData/简单测试_{timestamp}.pkl'
            with open(pickle_file, 'wb') as f:
                pickle.dump(data, f)
            print(f"原始数据已保存: {pickle_file}")
            
            # 尝试转换为CSV
            if isinstance(data, (pd.DataFrame, pd.Series)):
                csv_file = f'D:/QMT/UserData/简单测试_{timestamp}.csv'
                if isinstance(data, pd.DataFrame):
                    data.to_csv(csv_file, encoding='utf-8-sig', index=True)
                else:
                    data.to_frame().to_csv(csv_file, encoding='utf-8-sig', index=True)
                print(f"CSV文件已保存: {csv_file}")
        
        return data
        
    except Exception as e:
        print(f"简单测试失败: {e}")
        return None

# QMT策略函数
def init(ContextInfo):
    """策略初始化"""
    print("=== 财务数据调试策略初始化 ===")

def handlebar(ContextInfo):
    """策略主函数"""
    print("\n开始调试财务数据获取...")
    
    # 运行详细调试
    debug_result = debug_financial_data(ContextInfo)
    
    # 运行简单测试
    simple_result = simple_test_financial_data(ContextInfo)
    
    print("\n=== 调试总结 ===")
    if debug_result is not None or simple_result is not None:
        print("✓ 至少有一个测试成功，请检查保存的文件")
    else:
        print("✗ 所有测试都失败，请检查:")
        print("  1. QMT是否正常登录")
        print("  2. 是否下载了财务数据包")
        print("  3. 网络连接是否正常")
    
    return debug_result or simple_result

if __name__ == "__main__":
    print("请在QMT环境中运行此调试脚本")
    print("这个脚本会详细输出每一步的执行情况，帮助找出问题所在")
