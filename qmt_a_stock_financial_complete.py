# QMT A股财务数据获取完整版本
# 在QMT内置Python环境中运行

import pandas as pd
import time
from datetime import datetime, timedelta
import os

class AStockFinancialDataCollector:
    """A股财务数据收集器"""
    
    def __init__(self, context_info):
        self.context = context_info
        self.data_dir = 'D:/QMT/UserData/FinancialData/'
        self.ensure_data_directory()
        
    def ensure_data_directory(self):
        """确保数据目录存在"""
        try:
            if not os.path.exists(self.data_dir):
                os.makedirs(self.data_dir)
                print(f"创建数据目录: {self.data_dir}")
        except Exception as e:
            print(f"创建目录失败: {e}")
            self.data_dir = 'D:/QMT/UserData/'
    
    def get_all_a_stocks(self):
        """获取A股所有股票代码"""
        print("正在获取A股股票列表...")
        all_stocks = []
        
        try:
            # 方法1: 通过主要指数获取股票列表
            indices = [
                '000300.SH',  # 沪深300
                '000905.SH',  # 中证500
                '000016.SH',  # 上证50
                '399006.SZ',  # 创业板指
                '000001.SH',  # 上证指数
                '399001.SZ',  # 深证成指
            ]
            
            for index_code in indices:
                try:
                    stocks = self.context.get_sector(index_code)
                    if stocks:
                        all_stocks.extend(stocks)
                        print(f"从 {index_code} 获取到 {len(stocks)} 只股票")
                except Exception as e:
                    print(f"获取 {index_code} 成分股失败: {e}")
                    continue
            
            # 去重
            all_stocks = list(set(all_stocks))
            print(f"总共获取到 {len(all_stocks)} 只不重复的股票")
            
        except Exception as e:
            print(f"获取股票列表失败: {e}")
            # 备用股票列表
            all_stocks = self.get_backup_stock_list()
        
        return all_stocks
    
    def get_backup_stock_list(self):
        """备用股票列表"""
        backup_stocks = [
            # 银行股
            '000001.SZ', '002142.SZ', '600000.SH', '600036.SH', '601166.SH',
            '601169.SH', '601288.SH', '601328.SH', '601398.SH', '601939.SH',
            # 科技股
            '000002.SZ', '000858.SZ', '002415.SZ', '002594.SZ', '300059.SZ',
            '600519.SH', '600036.SH', '000858.SZ', '002415.SZ', '300014.SZ',
            # 消费股
            '000568.SZ', '000858.SZ', '002304.SZ', '600519.SH', '000895.SZ',
        ]
        print(f"使用备用股票列表，共 {len(backup_stocks)} 只股票")
        return backup_stocks
    
    def get_financial_fields(self):
        """获取财务数据字段定义"""
        fields = {
            # 资产负债表
            'balance_sheet': [
                'ASHAREBALANCESHEET.tot_assets',           # 总资产
                'ASHAREBALANCESHEET.tot_liab',             # 总负债
                'ASHAREBALANCESHEET.tot_shrhldr_eqy_excl_min_int', # 股东权益合计
                'ASHAREBALANCESHEET.monetary_cap',         # 货币资金
                'ASHAREBALANCESHEET.inventories',          # 存货
                'ASHAREBALANCESHEET.fix_assets',           # 固定资产
                'ASHAREBALANCESHEET.goodwill',             # 商誉
                'ASHAREBALANCESHEET.accounts_receiv',      # 应收账款
                'ASHAREBALANCESHEET.prepay',               # 预付款项
            ],
            
            # 利润表
            'income_statement': [
                'ASHAREINCOME.oper_rev',                   # 营业收入
                'ASHAREINCOME.oper_cost',                  # 营业成本
                'ASHAREINCOME.net_profit_excl_min_int_inc', # 净利润
                'ASHAREINCOME.net_profit_incl_min_int_inc', # 归母净利润
                'ASHAREINCOME.tot_profit',                 # 利润总额
                'ASHAREINCOME.oper_profit',                # 营业利润
                'ASHAREINCOME.basic_eps',                  # 基本每股收益
                'ASHAREINCOME.diluted_eps',                # 稀释每股收益
            ],
            
            # 现金流量表
            'cash_flow': [
                'ASHARECASHFLOW.net_cash_flows_oper_act',  # 经营活动现金流量净额
                'ASHARECASHFLOW.net_cash_flows_inv_act',   # 投资活动现金流量净额
                'ASHARECASHFLOW.net_cash_flows_fnc_act',   # 筹资活动现金流量净额
                'ASHARECASHFLOW.cash_recp_sg_and_rs',      # 销售商品收到的现金
            ],
            
            # 主要财务指标
            'key_indicators': [
                'CAPITALSTRUCTURE.total_capital',          # 总股本
                'CAPITALSTRUCTURE.flow_a_shares',          # 流通A股
            ]
        }
        
        # 合并所有字段
        all_fields = []
        for category, field_list in fields.items():
            all_fields.extend(field_list)
        
        return all_fields, fields
    
    def collect_financial_data(self, years_back=3, batch_size=30):
        """
        收集财务数据
        
        参数:
        years_back: 获取多少年的历史数据
        batch_size: 每批处理的股票数量
        """
        print(f"开始收集A股财务数据（最近{years_back}年）...")
        
        # 获取股票列表
        stock_list = self.get_all_a_stocks()
        if not stock_list:
            print("未能获取到股票列表，退出")
            return None
        
        # 获取字段定义
        all_fields, field_categories = self.get_financial_fields()
        
        # 设置时间范围
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=365*years_back)).strftime('%Y%m%d')
        
        print(f"数据时间范围: {start_date} 到 {end_date}")
        print(f"将分 {len(stock_list)//batch_size + 1} 批处理，每批 {batch_size} 只股票")
        
        all_data = []
        successful_batches = 0
        
        # 分批处理
        for i in range(0, len(stock_list), batch_size):
            batch_stocks = stock_list[i:i+batch_size]
            batch_num = i//batch_size + 1
            
            print(f"\n处理第 {batch_num} 批股票 ({len(batch_stocks)} 只)...")
            print(f"股票代码: {', '.join(batch_stocks[:5])}{'...' if len(batch_stocks) > 5 else ''}")
            
            try:
                # 获取财务数据
                financial_data = self.context.get_financial_data(
                    fieldList=all_fields,
                    stockList=batch_stocks,
                    startDate=start_date,
                    endDate=end_date,
                    report_type='announce_time'  # 按公告期获取
                )
                
                if financial_data is not None:
                    all_data.append({
                        'batch': batch_num,
                        'stocks': batch_stocks,
                        'data': financial_data
                    })
                    successful_batches += 1
                    print(f"✓ 第 {batch_num} 批数据获取成功")
                else:
                    print(f"✗ 第 {batch_num} 批数据为空")
                    
            except Exception as e:
                print(f"✗ 第 {batch_num} 批数据获取失败: {e}")
                continue
            
            # 添加延时避免请求过快
            time.sleep(2)
            
            # 每10批保存一次中间结果
            if batch_num % 10 == 0:
                self.save_intermediate_data(all_data, batch_num)
        
        print(f"\n数据收集完成！成功处理 {successful_batches} 批，共 {successful_batches * batch_size} 只股票")
        
        # 保存最终数据
        final_data = self.process_and_save_data(all_data, field_categories)
        
        return final_data
    
    def save_intermediate_data(self, data, batch_num):
        """保存中间数据"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{self.data_dir}中间数据_批次{batch_num}_{timestamp}.pkl"
            
            import pickle
            with open(filename, 'wb') as f:
                pickle.dump(data, f)
            print(f"中间数据已保存: {filename}")
            
        except Exception as e:
            print(f"保存中间数据失败: {e}")
    
    def process_and_save_data(self, all_data, field_categories):
        """处理并保存最终数据"""
        if not all_data:
            print("没有数据需要保存")
            return None
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        try:
            # 保存原始数据
            import pickle
            raw_filename = f"{self.data_dir}A股财务原始数据_{timestamp}.pkl"
            with open(raw_filename, 'wb') as f:
                pickle.dump(all_data, f)
            print(f"原始数据已保存: {raw_filename}")
            
            # 尝试处理为DataFrame格式
            processed_data = self.convert_to_dataframe(all_data)
            if processed_data is not None:
                csv_filename = f"{self.data_dir}A股财务数据_{timestamp}.csv"
                processed_data.to_csv(csv_filename, encoding='utf-8-sig', index=False)
                print(f"CSV数据已保存: {csv_filename}")
                
                # 如果数据量不大，也保存为Excel
                if len(processed_data) < 50000:
                    excel_filename = f"{self.data_dir}A股财务数据_{timestamp}.xlsx"
                    processed_data.to_excel(excel_filename, index=False)
                    print(f"Excel数据已保存: {excel_filename}")
            
            return all_data
            
        except Exception as e:
            print(f"保存数据失败: {e}")
            return all_data
    
    def convert_to_dataframe(self, all_data):
        """尝试将数据转换为DataFrame格式"""
        try:
            # 这里需要根据实际返回的数据格式进行调整
            # QMT的get_financial_data可能返回不同格式的数据
            print("正在转换数据格式...")
            
            # 简单的处理逻辑，实际使用时可能需要调整
            combined_df = pd.DataFrame()
            
            for batch_info in all_data:
                batch_data = batch_info['data']
                if isinstance(batch_data, pd.DataFrame):
                    combined_df = pd.concat([combined_df, batch_data], ignore_index=True)
                elif isinstance(batch_data, dict):
                    # 处理字典格式的数据
                    df = pd.DataFrame(batch_data)
                    combined_df = pd.concat([combined_df, df], ignore_index=True)
            
            if not combined_df.empty:
                print(f"数据转换成功，共 {len(combined_df)} 行数据")
                return combined_df
            else:
                print("转换后的DataFrame为空")
                return None
                
        except Exception as e:
            print(f"数据转换失败: {e}")
            return None

# QMT策略函数
def init(ContextInfo):
    """QMT策略初始化函数"""
    print("=== A股财务数据收集策略初始化 ===")
    print("策略功能：获取A股所有股票的财务数据")
    print("数据保存位置：D:/QMT/UserData/FinancialData/")

def handlebar(ContextInfo):
    """QMT策略主函数"""
    print("\n=== 开始执行A股财务数据收集 ===")
    
    # 创建数据收集器
    collector = AStockFinancialDataCollector(ContextInfo)
    
    # 收集财务数据（最近3年，每批30只股票）
    result = collector.collect_financial_data(years_back=3, batch_size=30)
    
    if result:
        print("\n=== 财务数据收集完成 ===")
        print("请查看 D:/QMT/UserData/FinancialData/ 目录下的文件")
    else:
        print("\n=== 财务数据收集失败 ===")
    
    return result

# 测试函数（在QMT环境外运行时）
if __name__ == "__main__":
    print("=== QMT A股财务数据获取工具 ===")
    print("使用说明：")
    print("1. 确保已登录QMT并下载了财务数据")
    print("2. 将此脚本复制到QMT策略编辑器中")
    print("3. 运行策略即可开始收集数据")
    print("4. 数据将保存到 D:/QMT/UserData/FinancialData/ 目录")
    print("\n注意事项：")
    print("- 首次运行可能需要较长时间")
    print("- 建议在非交易时间运行以避免影响行情接收")
    print("- 如果中途中断，可以从中间数据文件恢复")
