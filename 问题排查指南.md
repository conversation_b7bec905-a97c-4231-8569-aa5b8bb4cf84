# QMT财务数据获取问题排查指南

## 问题现象
运行脚本后没有看到数据，可能的表现：
- 脚本运行完成但没有生成CSV文件
- 生成的CSV文件是空的
- 控制台显示"数据为None"或类似错误
- 程序运行中断或报错

## 排查步骤

### 第一步：运行基础测试脚本

使用 `qmt_test_basic.py` 进行基础测试：

1. **在QMT中创建新策略**
2. **复制 `qmt_test_basic.py` 的全部代码**
3. **运行策略并查看输出**

这个脚本会逐步测试每个功能，帮助定位具体问题。

### 第二步：检查常见问题

#### 1. QMT登录状态
**检查项目**：
- [ ] QMT是否正常登录
- [ ] 网络连接是否稳定
- [ ] 是否有行情数据显示

**解决方法**：
- 重新登录QMT
- 检查网络连接
- 确认QMT客户端正常工作

#### 2. 财务数据包下载
**检查项目**：
- [ ] 是否下载了财务数据包
- [ ] 财务数据是否为最新版本
- [ ] 数据包是否完整

**下载方法**：
```
QMT菜单 → 工具 → 数据下载 → 财务数据
选择需要的数据包进行下载
```

#### 3. QMT版本兼容性
**检查项目**：
- [ ] QMT版本是否支持财务数据API
- [ ] Python环境是否正确配置

**解决方法**：
- 更新到最新版本的QMT
- 检查QMT的Python环境配置

#### 4. 字段名称正确性
**常见错误**：
- 字段名拼写错误
- 表名前缀错误
- 字段不存在

**正确的字段名格式**：
```python
# 正确格式
'ASHAREINCOME.oper_rev'           # 营业收入
'ASHAREBALANCESHEET.tot_assets'   # 总资产
'ASHARECASHFLOW.net_cash_flows_oper_act'  # 经营现金流

# 错误格式
'oper_rev'                        # 缺少表名
'INCOME.oper_rev'                 # 表名错误
'ASHAREINCOME.revenue'            # 字段名错误
```

#### 5. 时间范围设置
**检查项目**：
- [ ] 开始时间和结束时间格式是否正确
- [ ] 时间范围是否合理
- [ ] 是否有该时间段的数据

**正确格式**：
```python
start_date = '20230101'  # YYYYMMDD格式
end_date = '20231231'
```

### 第三步：使用调试脚本

如果基础测试仍有问题，使用 `qmt_debug_financial.py`：

1. **运行调试脚本**
2. **查看详细的输出信息**
3. **根据输出信息定位问题**

### 第四步：检查输出文件

#### 1. 检查文件是否生成
**文件位置**：
- `D:/QMT/UserData/`
- `D:/QMT/UserData/CSV_Data/`
- `D:/QMT/UserData/FinancialData/`

#### 2. 检查文件内容
```python
# 检查CSV文件
import pandas as pd
df = pd.read_csv('文件路径', encoding='utf-8-sig')
print(df.head())
print(df.info())

# 检查pickle文件
import pickle
with open('文件路径', 'rb') as f:
    data = pickle.load(f)
print(type(data))
print(data)
```

## 常见错误及解决方案

### 错误1：ContextInfo未定义
**错误信息**：`NameError: name 'ContextInfo' is not defined`

**原因**：脚本在QMT环境外运行

**解决方案**：
- 确保在QMT策略编辑器中运行
- 不要在普通Python环境中运行

### 错误2：get_financial_data方法不存在
**错误信息**：`AttributeError: 'ContextInfo' object has no attribute 'get_financial_data'`

**原因**：QMT版本过旧或API不支持

**解决方案**：
- 更新QMT到最新版本
- 检查QMT文档确认API可用性

### 错误3：数据为None
**错误信息**：控制台显示"数据为None"

**可能原因**：
1. 财务数据包未下载
2. 股票代码不存在
3. 时间范围内无数据
4. 字段名错误

**解决方案**：
```python
# 使用最简单的测试
data = ContextInfo.get_financial_data(
    fieldList=['ASHAREINCOME.oper_rev'],
    stockList=['000001.SZ'],
    startDate='20230101',
    endDate='20231231'
)
print(f"数据类型: {type(data)}")
print(f"数据内容: {data}")
```

### 错误4：CSV文件为空
**原因**：数据转换失败

**解决方案**：
1. 先保存原始数据（pickle格式）
2. 检查原始数据结构
3. 根据数据结构调整转换逻辑

### 错误5：编码问题
**错误信息**：CSV文件中文乱码

**解决方案**：
```python
# 保存时指定编码
df.to_csv(filename, encoding='utf-8-sig', index=False)

# 读取时指定编码
df = pd.read_csv(filename, encoding='utf-8-sig')
```

## 测试用的最简代码

如果所有方法都失败，尝试这个最简单的测试：

```python
def init(ContextInfo):
    print("策略初始化")

def handlebar(ContextInfo):
    print("开始最简测试...")
    
    try:
        # 最简单的调用
        data = ContextInfo.get_financial_data(
            ['ASHAREINCOME.oper_rev'],
            ['000001.SZ'],
            '20230101',
            '20231231'
        )
        
        print(f"数据类型: {type(data)}")
        print(f"数据: {data}")
        
        if data is not None:
            # 保存原始数据
            import pickle
            with open('D:/QMT/UserData/test_data.pkl', 'wb') as f:
                pickle.dump(data, f)
            print("数据已保存到 D:/QMT/UserData/test_data.pkl")
        
    except Exception as e:
        print(f"错误: {e}")
        print(f"错误类型: {type(e)}")
        import traceback
        traceback.print_exc()
```

## 联系技术支持

如果以上方法都无法解决问题，请提供以下信息：

1. **QMT版本信息**
2. **错误信息截图**
3. **控制台完整输出**
4. **测试脚本运行结果**
5. **系统环境信息**

## 替代方案

如果QMT财务数据API确实无法使用，可以考虑：

1. **使用其他数据源**：
   - Wind API
   - 东方财富API
   - 聚宽数据
   - Tushare

2. **手动导出数据**：
   - 从QMT界面手动导出财务数据
   - 使用Excel等工具处理

3. **使用第三方工具**：
   - 爬虫获取公开财务数据
   - 购买专业数据服务

## 预防措施

为避免类似问题，建议：

1. **定期更新QMT**
2. **定期下载最新财务数据包**
3. **保持网络连接稳定**
4. **备份重要数据**
5. **测试新功能前先用简单示例**
