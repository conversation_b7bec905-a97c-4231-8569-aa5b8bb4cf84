# QMT A股财务数据获取 - 简单CSV版本
# 专门针对CSV输出优化，简单易用

import pandas as pd
import time
from datetime import datetime

def get_financial_data_to_csv(ContextInfo):
    """
    获取A股财务数据并保存为CSV
    简化版本，专注CSV输出
    """
    print("=== 开始获取A股财务数据并转换为CSV ===")
    
    # 1. 获取股票列表
    print("1. 获取股票列表...")
    try:
        # 获取沪深300成分股
        hs300_stocks = ContextInfo.get_sector('000300.SH')
        print(f"   沪深300成分股: {len(hs300_stocks)} 只")
        
        # 获取中证500成分股
        zz500_stocks = ContextInfo.get_sector('000905.SH')
        print(f"   中证500成分股: {len(zz500_stocks)} 只")
        
        # 合并去重
        all_stocks = list(set(hs300_stocks + zz500_stocks))
        print(f"   合并后总计: {len(all_stocks)} 只股票")
        
    except Exception as e:
        print(f"   获取失败: {e}")
        # 使用示例股票
        all_stocks = [
            '000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ', '300014.SZ',
            '600000.SH', '600036.SH', '600519.SH', '601166.SH', '601318.SH'
        ]
        print(f"   使用示例股票: {len(all_stocks)} 只")
    
    # 2. 定义财务字段
    print("2. 定义财务字段...")
    fields = [
        'ASHAREBALANCESHEET.tot_assets',           # 总资产
        'ASHAREBALANCESHEET.tot_shrhldr_eqy_excl_min_int', # 股东权益
        'ASHAREINCOME.oper_rev',                   # 营业收入
        'ASHAREINCOME.net_profit_incl_min_int_inc', # 归母净利润
        'ASHAREINCOME.basic_eps',                  # 基本每股收益
        'ASHARECASHFLOW.net_cash_flows_oper_act',  # 经营现金流
        'CAPITALSTRUCTURE.total_capital',          # 总股本
    ]
    print(f"   财务字段数量: {len(fields)}")
    
    # 3. 设置时间范围
    print("3. 设置时间范围...")
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = datetime.now().replace(year=datetime.now().year-2).strftime('%Y%m%d')
    print(f"   时间范围: {start_date} 到 {end_date}")
    
    # 4. 分批获取数据
    print("4. 开始获取财务数据...")
    batch_size = 50
    all_rows = []  # 存储所有行数据
    
    for i in range(0, len(all_stocks), batch_size):
        batch_stocks = all_stocks[i:i+batch_size]
        batch_num = i//batch_size + 1
        
        print(f"   处理第 {batch_num} 批: {len(batch_stocks)} 只股票")
        
        try:
            # 获取财务数据
            data = ContextInfo.get_financial_data(
                fieldList=fields,
                stockList=batch_stocks,
                startDate=start_date,
                endDate=end_date,
                report_type='announce_time'
            )
            
            if data is not None:
                # 转换为行数据
                rows = convert_data_to_rows(data, batch_stocks, fields)
                if rows:
                    all_rows.extend(rows)
                    print(f"     ✓ 成功获取 {len(rows)} 行数据")
                else:
                    print(f"     ✗ 数据转换失败")
            else:
                print(f"     ✗ 数据获取失败")
                
        except Exception as e:
            print(f"     ✗ 处理出错: {e}")
            continue
        
        # 延时
        time.sleep(1)
    
    # 5. 创建DataFrame并保存CSV
    print("5. 保存CSV文件...")
    if all_rows:
        try:
            # 创建DataFrame
            df = pd.DataFrame(all_rows)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'D:/QMT/UserData/A股财务数据_{timestamp}.csv'
            
            # 保存CSV
            df.to_csv(filename, encoding='utf-8-sig', index=False)
            
            print(f"   ✓ CSV文件已保存: {filename}")
            print(f"   数据行数: {len(df)}")
            print(f"   数据列数: {len(df.columns)}")
            
            # 显示前几行数据
            print("\n6. 数据预览:")
            print(df.head())
            
            # 保存字段说明
            save_field_description(filename.replace('.csv', '_字段说明.csv'))
            
            return df
            
        except Exception as e:
            print(f"   ✗ 保存CSV失败: {e}")
            return None
    else:
        print("   ✗ 没有数据可保存")
        return None

def convert_data_to_rows(data, stock_codes, field_names):
    """
    将QMT财务数据转换为行格式
    每行包含：股票代码、日期、各个财务字段值
    """
    rows = []
    
    try:
        if isinstance(data, pd.DataFrame):
            # DataFrame格式
            for index, row in data.iterrows():
                row_dict = {'股票代码': '', '日期': ''}
                row_dict.update(row.to_dict())
                rows.append(row_dict)
                
        elif isinstance(data, pd.Panel):
            # Panel格式 (股票 x 时间 x 字段)
            for stock in data.items:
                stock_data = data[stock]
                for date_idx in stock_data.index:
                    row_dict = {
                        '股票代码': stock,
                        '日期': str(date_idx)
                    }
                    for field in stock_data.columns:
                        value = stock_data.loc[date_idx, field]
                        # 清理字段名（去掉表名前缀）
                        clean_field = field.split('.')[-1] if '.' in field else field
                        row_dict[clean_field] = value
                    rows.append(row_dict)
                    
        elif isinstance(data, dict):
            # 字典格式
            for stock_code in stock_codes:
                if stock_code in data:
                    stock_data = data[stock_code]
                    if isinstance(stock_data, dict):
                        row_dict = {'股票代码': stock_code, '日期': '最新'}
                        for field, value in stock_data.items():
                            clean_field = field.split('.')[-1] if '.' in field else field
                            row_dict[clean_field] = value
                        rows.append(row_dict)
                        
        elif isinstance(data, pd.Series):
            # Series格式
            row_dict = {'股票代码': stock_codes[0] if stock_codes else '', '日期': '最新'}
            for field, value in data.items():
                clean_field = field.split('.')[-1] if '.' in field else field
                row_dict[clean_field] = value
            rows.append(row_dict)
            
        else:
            print(f"     未知数据格式: {type(data)}")
            
    except Exception as e:
        print(f"     数据转换出错: {e}")
    
    return rows

def save_field_description(filename):
    """保存字段说明文件"""
    try:
        descriptions = [
            {'字段名': '股票代码', '说明': '股票代码'},
            {'字段名': '日期', '说明': '数据日期'},
            {'字段名': 'tot_assets', '说明': '总资产'},
            {'字段名': 'tot_shrhldr_eqy_excl_min_int', '说明': '股东权益合计'},
            {'字段名': 'oper_rev', '说明': '营业收入'},
            {'字段名': 'net_profit_incl_min_int_inc', '说明': '归母净利润'},
            {'字段名': 'basic_eps', '说明': '基本每股收益'},
            {'字段名': 'net_cash_flows_oper_act', '说明': '经营活动现金流量净额'},
            {'字段名': 'total_capital', '说明': '总股本'},
        ]
        
        desc_df = pd.DataFrame(descriptions)
        desc_df.to_csv(filename, encoding='utf-8-sig', index=False)
        print(f"   ✓ 字段说明已保存: {filename}")
        
    except Exception as e:
        print(f"   保存字段说明失败: {e}")

def quick_csv_export(ContextInfo, stock_list=None, custom_fields=None):
    """
    快速CSV导出函数
    
    参数:
    stock_list: 自定义股票列表，如 ['000001.SZ', '600000.SH']
    custom_fields: 自定义字段列表
    """
    print("=== 快速CSV导出 ===")
    
    # 使用自定义股票列表或默认列表
    if stock_list is None:
        stock_list = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '600519.SH']
    
    # 使用自定义字段或默认字段
    if custom_fields is None:
        custom_fields = [
            'ASHAREINCOME.oper_rev',                   # 营业收入
            'ASHAREINCOME.net_profit_incl_min_int_inc', # 归母净利润
            'ASHAREINCOME.basic_eps',                  # 基本每股收益
        ]
    
    print(f"股票数量: {len(stock_list)}")
    print(f"字段数量: {len(custom_fields)}")
    
    # 时间范围
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = datetime.now().replace(year=datetime.now().year-1).strftime('%Y%m%d')
    
    try:
        # 获取数据
        data = ContextInfo.get_financial_data(
            fieldList=custom_fields,
            stockList=stock_list,
            startDate=start_date,
            endDate=end_date,
            report_type='announce_time'
        )
        
        if data is not None:
            # 转换并保存
            rows = convert_data_to_rows(data, stock_list, custom_fields)
            if rows:
                df = pd.DataFrame(rows)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f'D:/QMT/UserData/快速导出_{timestamp}.csv'
                df.to_csv(filename, encoding='utf-8-sig', index=False)
                print(f"✓ 快速导出完成: {filename}")
                return df
        
        print("✗ 快速导出失败")
        return None
        
    except Exception as e:
        print(f"✗ 快速导出出错: {e}")
        return None

# QMT策略函数
def init(ContextInfo):
    """策略初始化"""
    print("=== A股财务数据CSV导出策略 ===")

def handlebar(ContextInfo):
    """策略主函数"""
    # 方式1: 完整导出（推荐）
    result = get_financial_data_to_csv(ContextInfo)
    
    # 方式2: 快速导出（可选，取消注释使用）
    # custom_stocks = ['000001.SZ', '000002.SZ', '600000.SH']
    # quick_result = quick_csv_export(ContextInfo, custom_stocks)
    
    return result

# 使用说明
if __name__ == "__main__":
    print("=== QMT财务数据CSV导出工具 ===")
    print("特点：")
    print("- 专门优化CSV输出格式")
    print("- 自动处理各种数据格式")
    print("- 包含字段说明文件")
    print("- 支持快速导出模式")
    print("\n使用方法：")
    print("1. 在QMT策略编辑器中运行")
    print("2. 查看 D:/QMT/UserData/ 目录下的CSV文件")
    print("3. 同时会生成字段说明文件")
