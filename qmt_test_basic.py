# QMT基础测试脚本 - 排查数据获取问题
# 最简单的测试版本

import pandas as pd
from datetime import datetime

def test_basic_functions(ContextInfo):
    """
    测试QMT基础功能
    """
    print("=== QMT基础功能测试 ===")
    
    # 测试1: 检查ContextInfo是否可用
    print("1. 检查ContextInfo...")
    try:
        print(f"   ContextInfo类型: {type(ContextInfo)}")
        available_methods = [method for method in dir(ContextInfo) if 'get' in method.lower()]
        print(f"   可用的get方法: {available_methods[:10]}")  # 只显示前10个
    except Exception as e:
        print(f"   ContextInfo检查失败: {e}")
        return False
    
    # 测试2: 测试获取指数成分股
    print("\n2. 测试获取指数成分股...")
    try:
        stocks = ContextInfo.get_sector('000300.SH')  # 沪深300
        if stocks:
            print(f"   ✓ 成功获取沪深300成分股: {len(stocks)} 只")
            print(f"   前5只股票: {stocks[:5]}")
            return stocks[:5]  # 返回前5只用于后续测试
        else:
            print("   ✗ 未获取到股票数据")
            return ['000001.SZ', '000002.SZ']  # 备用股票
    except Exception as e:
        print(f"   ✗ 获取指数成分股失败: {e}")
        return ['000001.SZ', '000002.SZ']  # 备用股票

def test_financial_data_step_by_step(ContextInfo, test_stocks):
    """
    逐步测试财务数据获取
    """
    print("\n=== 逐步测试财务数据获取 ===")
    
    # 测试参数
    test_stock = test_stocks[0]
    simple_field = 'ASHAREINCOME.oper_rev'  # 营业收入
    end_date = '20231231'
    start_date = '20230101'
    
    print(f"测试股票: {test_stock}")
    print(f"测试字段: {simple_field}")
    print(f"时间范围: {start_date} 到 {end_date}")
    
    # 步骤1: 最简单的调用
    print("\n步骤1: 最简单的财务数据获取...")
    try:
        data1 = ContextInfo.get_financial_data(
            fieldList=[simple_field],
            stockList=[test_stock],
            startDate=start_date,
            endDate=end_date
        )
        
        print(f"   结果类型: {type(data1)}")
        if data1 is not None:
            print(f"   ✓ 获取成功")
            print_data_info(data1, "步骤1数据")
            save_test_data(data1, "step1_simple")
        else:
            print(f"   ✗ 数据为None")
            
    except Exception as e:
        print(f"   ✗ 步骤1失败: {e}")
        data1 = None
    
    # 步骤2: 添加report_type参数
    print("\n步骤2: 添加report_type参数...")
    try:
        data2 = ContextInfo.get_financial_data(
            fieldList=[simple_field],
            stockList=[test_stock],
            startDate=start_date,
            endDate=end_date,
            report_type='announce_time'
        )
        
        print(f"   结果类型: {type(data2)}")
        if data2 is not None:
            print(f"   ✓ 获取成功")
            print_data_info(data2, "步骤2数据")
            save_test_data(data2, "step2_with_report_type")
        else:
            print(f"   ✗ 数据为None")
            
    except Exception as e:
        print(f"   ✗ 步骤2失败: {e}")
        data2 = None
    
    # 步骤3: 尝试多个字段
    print("\n步骤3: 测试多个字段...")
    try:
        multi_fields = [
            'ASHAREINCOME.oper_rev',
            'ASHAREINCOME.net_profit_incl_min_int_inc'
        ]
        
        data3 = ContextInfo.get_financial_data(
            fieldList=multi_fields,
            stockList=[test_stock],
            startDate=start_date,
            endDate=end_date,
            report_type='announce_time'
        )
        
        print(f"   结果类型: {type(data3)}")
        if data3 is not None:
            print(f"   ✓ 获取成功")
            print_data_info(data3, "步骤3数据")
            save_test_data(data3, "step3_multi_fields")
        else:
            print(f"   ✗ 数据为None")
            
    except Exception as e:
        print(f"   ✗ 步骤3失败: {e}")
        data3 = None
    
    # 步骤4: 尝试多只股票
    print("\n步骤4: 测试多只股票...")
    try:
        data4 = ContextInfo.get_financial_data(
            fieldList=[simple_field],
            stockList=test_stocks[:2],  # 只测试前2只
            startDate=start_date,
            endDate=end_date,
            report_type='announce_time'
        )
        
        print(f"   结果类型: {type(data4)}")
        if data4 is not None:
            print(f"   ✓ 获取成功")
            print_data_info(data4, "步骤4数据")
            save_test_data(data4, "step4_multi_stocks")
        else:
            print(f"   ✗ 数据为None")
            
    except Exception as e:
        print(f"   ✗ 步骤4失败: {e}")
        data4 = None
    
    # 返回第一个成功的数据
    for data in [data1, data2, data3, data4]:
        if data is not None:
            return data
    
    return None

def print_data_info(data, data_name):
    """打印数据信息"""
    print(f"   {data_name}信息:")
    
    if isinstance(data, pd.DataFrame):
        print(f"     DataFrame: {data.shape}")
        print(f"     列名: {list(data.columns)}")
        if not data.empty:
            print(f"     前2行:")
            for i, (idx, row) in enumerate(data.head(2).iterrows()):
                print(f"       行{i}: {dict(row)}")
    
    elif isinstance(data, pd.Panel):
        print(f"     Panel: items={len(data.items)}, major_axis={len(data.major_axis)}, minor_axis={len(data.minor_axis)}")
        print(f"     股票: {list(data.items)}")
        print(f"     时间: {list(data.major_axis)[:3]}...")
        print(f"     字段: {list(data.minor_axis)}")
    
    elif isinstance(data, dict):
        print(f"     字典: {len(data)} 个键")
        for key, value in list(data.items())[:2]:
            print(f"       {key}: {type(value)}")
            if hasattr(value, 'shape'):
                print(f"         形状: {value.shape}")
    
    elif isinstance(data, pd.Series):
        print(f"     Series: 长度={len(data)}")
        print(f"     索引: {list(data.index)[:3]}...")
        print(f"     前3个值: {dict(data.head(3))}")
    
    else:
        print(f"     其他类型: {type(data)}")
        print(f"     内容: {str(data)[:100]}...")

def save_test_data(data, filename_prefix):
    """保存测试数据"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存原始数据
        import pickle
        pickle_file = f'D:/QMT/UserData/测试_{filename_prefix}_{timestamp}.pkl'
        with open(pickle_file, 'wb') as f:
            pickle.dump(data, f)
        print(f"     原始数据已保存: {pickle_file}")
        
        # 尝试保存为CSV
        csv_file = f'D:/QMT/UserData/测试_{filename_prefix}_{timestamp}.csv'
        
        if isinstance(data, pd.DataFrame):
            data.to_csv(csv_file, encoding='utf-8-sig', index=True)
            print(f"     CSV已保存: {csv_file}")
        elif isinstance(data, pd.Series):
            data.to_frame().to_csv(csv_file, encoding='utf-8-sig', index=True)
            print(f"     CSV已保存: {csv_file}")
        elif isinstance(data, pd.Panel):
            # Panel转换为DataFrame
            df_list = []
            for stock in data.items:
                stock_data = data[stock].copy()
                stock_data['股票代码'] = stock
                df_list.append(stock_data)
            if df_list:
                combined_df = pd.concat(df_list)
                combined_df.to_csv(csv_file, encoding='utf-8-sig', index=True)
                print(f"     Panel转CSV已保存: {csv_file}")
        elif isinstance(data, dict):
            # 字典转换为DataFrame
            rows = []
            for key, value in data.items():
                if isinstance(value, dict):
                    row = {'键': key}
                    row.update(value)
                    rows.append(row)
                else:
                    rows.append({'键': key, '值': str(value)})
            if rows:
                df = pd.DataFrame(rows)
                df.to_csv(csv_file, encoding='utf-8-sig', index=False)
                print(f"     字典转CSV已保存: {csv_file}")
        
    except Exception as e:
        print(f"     保存失败: {e}")

def check_qmt_environment(ContextInfo):
    """检查QMT环境"""
    print("\n=== 检查QMT环境 ===")
    
    # 检查当前时间
    print(f"当前时间: {datetime.now()}")
    
    # 检查可用方法
    financial_methods = [method for method in dir(ContextInfo) if 'financial' in method.lower()]
    print(f"财务相关方法: {financial_methods}")
    
    # 检查数据目录
    import os
    data_dirs = ['D:/QMT/', 'D:/QMT/UserData/']
    for directory in data_dirs:
        if os.path.exists(directory):
            print(f"✓ 目录存在: {directory}")
        else:
            print(f"✗ 目录不存在: {directory}")
            try:
                os.makedirs(directory)
                print(f"  已创建目录: {directory}")
            except:
                print(f"  无法创建目录: {directory}")

# QMT策略函数
def init(ContextInfo):
    """策略初始化"""
    print("=== QMT财务数据基础测试策略 ===")
    print("目标: 找出数据获取问题的根本原因")

def handlebar(ContextInfo):
    """策略主函数"""
    print("\n开始基础测试...")
    
    # 检查环境
    check_qmt_environment(ContextInfo)
    
    # 测试基础功能
    test_stocks = test_basic_functions(ContextInfo)
    
    if test_stocks:
        # 逐步测试财务数据
        result = test_financial_data_step_by_step(ContextInfo, test_stocks)
        
        if result is not None:
            print(f"\n=== 测试成功 ===")
            print(f"成功获取到数据，类型: {type(result)}")
            print(f"请检查 D:/QMT/UserData/ 目录下的测试文件")
        else:
            print(f"\n=== 测试失败 ===")
            print(f"所有步骤都未能获取到数据")
            print(f"可能的原因:")
            print(f"1. 财务数据包未下载或已过期")
            print(f"2. QMT版本不兼容")
            print(f"3. 网络连接问题")
            print(f"4. 字段名称错误")
    else:
        print(f"\n=== 基础功能测试失败 ===")
        print(f"无法获取股票列表，请检查QMT连接状态")
    
    return result

if __name__ == "__main__":
    print("请在QMT环境中运行此测试脚本")
    print("这个脚本会逐步测试每个功能，帮助定位问题")
