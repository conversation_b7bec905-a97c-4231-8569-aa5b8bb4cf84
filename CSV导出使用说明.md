# QMT A股财务数据CSV导出使用说明

## 概述

本工具专门用于将QMT获取的A股财务数据转换并保存为CSV格式，方便在Excel、Python、R等工具中进一步分析。

## 文件说明

### 1. `qmt_simple_csv.py` - 推荐使用
- **特点**: 专门针对CSV输出优化
- **功能**: 自动处理各种QMT数据格式并转换为标准CSV
- **输出**: 清晰的表格格式，包含字段说明

### 2. `qmt_financial_to_csv.py` - 完整版
- **特点**: 功能全面，支持大批量数据处理
- **功能**: 分批处理、中间备份、详细日志
- **输出**: 多个CSV文件和数据字典

### 3. `qmt_simple_financial.py` - 增强版
- **特点**: 在原有功能基础上增加了CSV导出
- **功能**: 同时支持pickle和CSV格式输出

## 快速开始

### 第一步：选择合适的脚本

**初学者推荐**: 使用 `qmt_simple_csv.py`
```python
# 简单易用，专注CSV输出
# 自动处理数据格式转换
# 包含字段说明文件
```

**大量数据处理**: 使用 `qmt_financial_to_csv.py`
```python
# 支持大批量股票数据
# 分批处理避免内存问题
# 自动备份中间结果
```

### 第二步：在QMT中运行

1. **打开QMT策略编辑器**
2. **创建新策略**
3. **复制脚本代码**
4. **运行策略**

### 第三步：查看CSV文件

文件保存位置：
- `D:/QMT/UserData/` (简单版)
- `D:/QMT/UserData/CSV_Data/` (完整版)

## CSV文件格式说明

### 标准CSV格式
```csv
股票代码,日期,tot_assets,oper_rev,net_profit_incl_min_int_inc,basic_eps
000001.SZ,20231231,1234567890,987654321,123456789,1.23
000002.SZ,20231231,2345678901,876543210,234567890,2.34
```

### 字段说明
| 字段名 | 中文说明 | 单位 |
|--------|----------|------|
| 股票代码 | 股票代码 | - |
| 日期 | 数据日期 | YYYYMMDD |
| tot_assets | 总资产 | 元 |
| tot_shrhldr_eqy_excl_min_int | 股东权益合计 | 元 |
| monetary_cap | 货币资金 | 元 |
| oper_rev | 营业收入 | 元 |
| net_profit_incl_min_int_inc | 归母净利润 | 元 |
| basic_eps | 基本每股收益 | 元/股 |
| net_cash_flows_oper_act | 经营现金流 | 元 |
| total_capital | 总股本 | 股 |

## 在Excel中使用CSV文件

### 打开CSV文件
1. **直接双击CSV文件**（可能出现编码问题）
2. **推荐方法**：
   - 打开Excel
   - 选择"数据" → "从文本/CSV"
   - 选择CSV文件
   - 设置编码为"UTF-8"
   - 点击"加载"

### 数据分析示例
```excel
# 计算ROE (净资产收益率)
=净利润/股东权益

# 计算营业利润率
=营业利润/营业收入

# 计算资产负债率
=总负债/总资产
```

## 在Python中使用CSV文件

### 读取CSV数据
```python
import pandas as pd

# 读取CSV文件
df = pd.read_csv('D:/QMT/UserData/A股财务数据_20231201_143022.csv', 
                 encoding='utf-8-sig')

# 查看数据基本信息
print(df.info())
print(df.head())

# 查看数据统计
print(df.describe())
```

### 数据分析示例
```python
# 计算财务比率
df['ROE'] = df['net_profit_incl_min_int_inc'] / df['tot_shrhldr_eqy_excl_min_int']
df['营业利润率'] = df['oper_profit'] / df['oper_rev']
df['资产负债率'] = df['tot_liab'] / df['tot_assets']

# 筛选优质股票
good_stocks = df[
    (df['ROE'] > 0.15) &  # ROE > 15%
    (df['营业利润率'] > 0.1) &  # 营业利润率 > 10%
    (df['资产负债率'] < 0.6)   # 资产负债率 < 60%
]

print(f"筛选出 {len(good_stocks)} 只优质股票")
```

### 数据可视化
```python
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']

# ROE分布图
plt.figure(figsize=(10, 6))
plt.hist(df['ROE'], bins=50, alpha=0.7)
plt.title('ROE分布图')
plt.xlabel('ROE')
plt.ylabel('频数')
plt.show()

# 营业收入 vs 净利润散点图
plt.figure(figsize=(10, 6))
plt.scatter(df['oper_rev'], df['net_profit_incl_min_int_inc'], alpha=0.6)
plt.title('营业收入 vs 净利润')
plt.xlabel('营业收入')
plt.ylabel('净利润')
plt.show()
```

## 在R中使用CSV文件

### 读取数据
```r
# 读取CSV文件
df <- read.csv("D:/QMT/UserData/A股财务数据_20231201_143022.csv", 
               fileEncoding = "UTF-8")

# 查看数据结构
str(df)
head(df)
summary(df)
```

### 数据分析
```r
# 计算财务比率
df$ROE <- df$net_profit_incl_min_int_inc / df$tot_shrhldr_eqy_excl_min_int
df$profit_margin <- df$oper_profit / df$oper_rev

# 数据可视化
library(ggplot2)

# ROE分布图
ggplot(df, aes(x = ROE)) +
  geom_histogram(bins = 50, fill = "blue", alpha = 0.7) +
  labs(title = "ROE分布图", x = "ROE", y = "频数")

# 营业收入 vs 净利润
ggplot(df, aes(x = oper_rev, y = net_profit_incl_min_int_inc)) +
  geom_point(alpha = 0.6) +
  labs(title = "营业收入 vs 净利润", x = "营业收入", y = "净利润")
```

## 常见问题解决

### 1. CSV文件乱码
**问题**: 在Excel中打开CSV文件显示乱码
**解决**: 
- 使用Excel的"从文本/CSV"功能导入
- 设置编码为UTF-8
- 或者用记事本打开CSV文件，另存为ANSI编码

### 2. 数据格式问题
**问题**: 数字显示为文本格式
**解决**:
```python
# 在Python中转换数据类型
numeric_columns = ['tot_assets', 'oper_rev', 'net_profit_incl_min_int_inc']
for col in numeric_columns:
    df[col] = pd.to_numeric(df[col], errors='coerce')
```

### 3. 缺失数据处理
**问题**: 某些字段有缺失值
**解决**:
```python
# 查看缺失值
print(df.isnull().sum())

# 删除有缺失值的行
df_clean = df.dropna()

# 或者用均值填充
df['tot_assets'].fillna(df['tot_assets'].mean(), inplace=True)
```

### 4. 文件过大
**问题**: CSV文件太大，难以处理
**解决**:
- 使用分批处理版本的脚本
- 在Python中分块读取：
```python
# 分块读取大文件
chunk_size = 10000
for chunk in pd.read_csv('large_file.csv', chunksize=chunk_size):
    # 处理每个chunk
    process_chunk(chunk)
```

## 自定义配置

### 修改输出字段
```python
# 在脚本中修改financial_fields列表
financial_fields = [
    'ASHAREINCOME.oper_rev',           # 营业收入
    'ASHAREINCOME.net_profit_incl_min_int_inc',  # 净利润
    # 添加你需要的其他字段
]
```

### 修改股票范围
```python
# 自定义股票列表
custom_stocks = ['000001.SZ', '000002.SZ', '600000.SH']
```

### 修改时间范围
```python
# 获取更长时间的数据
start_date = datetime.now().replace(year=datetime.now().year-5).strftime('%Y%m%d')
```

## 最佳实践

1. **定期更新**: 建议每季度重新获取一次数据
2. **数据备份**: 保留原始CSV文件作为备份
3. **数据验证**: 获取数据后先检查数据的完整性和合理性
4. **分批处理**: 处理大量股票时使用分批处理避免内存问题
5. **错误处理**: 注意处理数据获取失败的情况

## 技术支持

如果遇到问题：
1. 检查QMT是否正常登录
2. 确认已下载财务数据包
3. 查看脚本运行日志中的错误信息
4. 尝试减少股票数量或字段数量进行测试
