# QMT A股财务数据获取工具使用说明

## 概述

本工具提供了三个版本的QMT财务数据获取脚本，帮助您获取A股所有股票的财务数据。

## 文件说明

### 1. `qmt_simple_financial.py` - 推荐使用
- **特点**: 简化版本，易于使用和理解
- **功能**: 获取主要指数成分股的财务数据
- **适用**: 初学者和快速获取数据的场景

### 2. `qmt_a_stock_financial_complete.py` - 完整版
- **特点**: 功能完整，包含详细的错误处理和数据管理
- **功能**: 获取更全面的A股财务数据，支持中间数据保存
- **适用**: 需要大量数据和高可靠性的场景

### 3. `qmt_builtin_financial.py` - 基础版
- **特点**: 基于原始代码改进的版本
- **功能**: 基本的财务数据获取功能
- **适用**: 了解基本原理和自定义开发

## 使用步骤

### 第一步：准备工作

1. **确保QMT已登录**
   - 打开QMT客户端并完成登录
   - 确保网络连接正常

2. **下载财务数据**
   - 在QMT中下载财务数据包
   - 路径：工具 → 数据下载 → 财务数据

3. **创建数据目录**
   - 确保 `D:/QMT/UserData/` 目录存在
   - 或者修改脚本中的保存路径

### 第二步：运行脚本

#### 方法一：使用简化版（推荐）

1. 打开QMT策略编辑器
2. 创建新策略
3. 复制 `qmt_simple_financial.py` 的全部内容
4. 粘贴到策略编辑器中
5. 点击"运行"按钮

#### 方法二：使用完整版

1. 同样的步骤，但使用 `qmt_a_stock_financial_complete.py`
2. 适合需要处理大量数据的情况

### 第三步：查看结果

1. **控制台输出**
   - 查看QMT策略运行窗口的输出信息
   - 了解数据获取进度和结果

2. **数据文件**
   - 文件保存在 `D:/QMT/UserData/` 或 `D:/QMT/UserData/FinancialData/`
   - 文件格式：`.pkl`（推荐）、`.csv`、`.xlsx`

## 获取的财务数据字段

### 资产负债表字段
- `tot_assets`: 总资产
- `tot_liab`: 总负债
- `tot_shrhldr_eqy_excl_min_int`: 股东权益合计
- `monetary_cap`: 货币资金
- `inventories`: 存货
- `fix_assets`: 固定资产
- `goodwill`: 商誉

### 利润表字段
- `oper_rev`: 营业收入
- `oper_cost`: 营业成本
- `net_profit_incl_min_int_inc`: 归母净利润
- `tot_profit`: 利润总额
- `oper_profit`: 营业利润
- `basic_eps`: 基本每股收益

### 现金流量表字段
- `net_cash_flows_oper_act`: 经营活动现金流量净额
- `net_cash_flows_inv_act`: 投资活动现金流量净额
- `net_cash_flows_fnc_act`: 筹资活动现金流量净额

### 股本结构字段
- `total_capital`: 总股本
- `flow_a_shares`: 流通A股

## 数据读取示例

### 读取pickle文件
```python
import pickle
import pandas as pd

# 读取数据
with open('D:/QMT/UserData/A股财务数据_20231201_143022.pkl', 'rb') as f:
    data = pickle.load(f)

# 查看数据结构
print(type(data))
print(len(data))
```

### 读取CSV文件
```python
import pandas as pd

# 读取CSV文件
df = pd.read_csv('D:/QMT/UserData/A股财务数据_20231201_143022.csv', encoding='utf-8-sig')

# 查看数据
print(df.head())
print(df.info())
```

## 常见问题解决

### 1. 获取股票列表失败
**问题**: 无法获取指数成分股
**解决**: 
- 检查网络连接
- 确保已下载相关指数数据
- 脚本会自动使用备用股票列表

### 2. 财务数据获取失败
**问题**: 某些股票的财务数据获取失败
**解决**:
- 确保已下载财务数据包
- 检查股票代码格式是否正确
- 脚本会跳过失败的股票继续处理

### 3. 数据保存失败
**问题**: 无法保存到指定目录
**解决**:
- 检查目录是否存在和有写入权限
- 修改脚本中的保存路径
- 确保磁盘空间充足

### 4. 内存不足
**问题**: 处理大量数据时内存不足
**解决**:
- 减少batch_size参数
- 使用完整版的中间数据保存功能
- 分多次运行获取不同时间段的数据

## 自定义配置

### 修改获取的股票范围
```python
# 在脚本中找到股票列表获取部分，修改为：
stock_list = ['000001.SZ', '000002.SZ', '600000.SH']  # 自定义股票列表
```

### 修改时间范围
```python
# 修改年份参数
start_date = datetime.now().replace(year=datetime.now().year-5).strftime('%Y%m%d')  # 获取5年数据
```

### 修改财务字段
```python
# 添加或删除需要的字段
financial_fields = [
    'ASHAREINCOME.oper_rev',  # 营业收入
    'ASHAREINCOME.net_profit_incl_min_int_inc',  # 净利润
    # 添加更多字段...
]
```

## 注意事项

1. **运行时间**: 首次运行可能需要较长时间，建议在非交易时间运行
2. **数据更新**: 财务数据通常按季度更新，建议定期重新获取
3. **存储空间**: 大量数据可能占用较多磁盘空间，注意清理旧文件
4. **网络稳定**: 确保网络连接稳定，避免数据获取中断
5. **QMT版本**: 确保使用较新版本的QMT，以获得最佳兼容性

## 技术支持

如果遇到问题，可以：
1. 检查QMT官方文档
2. 查看脚本中的错误输出信息
3. 根据错误信息调整参数或配置
4. 尝试使用不同版本的脚本

## 更新日志

- **v1.0**: 基础版本，支持基本财务数据获取
- **v2.0**: 添加简化版本，提高易用性
- **v3.0**: 添加完整版本，支持大规模数据处理和错误恢复
